version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_USER=AUDITSAM_APPL
      - DB_PASSWORD=3T25M#EOPF
      - DB_HOST=nt-auditsam.nhso.go.th
      - DB_PORT=1525
      - DB_SERVICE=AUDITSAM.gdcc.onde.go.th
      - DB_RETRY_COUNT=20
      - DB_RETRY_DELAY=3
      - DB_PROTOCOL=tcp
      - DB_SSL_SERVER_DN_MATCH=yes
    restart: unless-stopped
    volumes:
      # Important: Files are stored in /singleaudit/uploads/ on the server
      # This maps to /app/uploads in the container
      - /singleaudit/uploads:/app/uploads
      - ./logs:/app/logs
