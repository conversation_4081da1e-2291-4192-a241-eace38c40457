from datetime import datetime
from pathlib import Path
from uuid import uuid4
from sqlalchemy import Sequence
from sqlmodel import Relationship, SQLModel, Field, Session, select
from typing import Union

from app.services.datetime_service import get_current_bangkok_datetime


class SamJobAuditBase(SQLModel):
    job_no: str | None = Field(
        default=None,
        max_length=20,
        description="JOB number created (A-BBBB-YYMMZZXXX, JA = prefix for the list, BBBB = POST or PRE type, YY = fiscal year, MM = month created, ZZ = zone code, XXX = sequence number created) e.g., JA-POST-671115001",
        alias="JOB_NO",
    )
    job_seq: int | None = Field(
        default=None,
        description="Sequence number for data verification in the fiscal year and zone",
        alias="JOB_SEQ",
    )
    job_state: int | None = Field(
        default=None,
        description="Latest processing step",
        alias="JOB_STATE",
    )
    job_min_state: int | None = Field(
        default=None,
        description="Starting step of the process",
        alias="JOB_MIN_STATE",
    )
    job_max_state: int | None = Field(
        default=None,
        description="Final step of the process",
        alias="JOB_MAX_STATE",
    )
    job_type: str | None = Field(
        default=None,
        max_length=2,
        description="Job type (N = normal inspection, O = inspection at the service unit)",
        alias="JOB_TYPE",
    )
    master_audit_type_id: int | None = Field(
        default=None,
        foreign_key="SAM_MASTER_AUDIT_TYPE.id",
        description="Audit type ID from SAM_MASTER_AUDIT_TYPE",
        alias="MASTER_AUDIT_TYPE_ID",
    )
    gov_year: str | None = Field(
        default=None,
        max_length=4,
        description="Fiscal year",
        alias="GOV_YEAR",
    )
    nhso_zone: str | None = Field(
        default=None,
        max_length=2,
        description="Zone code according to NHSO",
        alias="NHSO_ZONE",
    )
    nhso_zonename: str | None = Field(
        default=None,
        max_length=255,
        description="Zone name according to NHSO",
        alias="NHSO_ZONENAME",
    )
    total_case: int | None = Field(
        default=None,
        description="Total number of cases to be inspected in the JOB",
        alias="TOTAL_CASE",
    )
    complete_case: int | None = Field(
        default=None,
        description="Number of completed and returned cases",
        alias="COMPLETE_CASE",
    )
    process_status: str | None = Field(
        default=None,
        max_length=5,
        description="Data processing status",
        alias="PROCESS_STATUS",
    )
    status: str | None = Field(
        default=None,
        max_length=5,
        description="Data status",
        alias="STATUS",
    )
    created_date: datetime | None = Field(
        default=None,
        description="Date and time of record creation",
        alias="CREATED_DATE",
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None,
        description="Date and time of record update",
        alias="UPDATED_DATE",
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamJobAudit(SamJobAuditBase, table=True):  # type: ignore
    __tablename__ = "SAM_JOB_AUDIT"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_JOB_AUDIT").next_value(),
        primary_key=True,
        description="Generated from SAM_SEQ_JOB_AUDIT",
        alias="ID",
    )

    audit_type: "SamMasterAuditType" = Relationship()
    transaction: list["SamJobAuditTransaction"] = Relationship(
        back_populates="job_audit",
    )
    medical_records: list["SamJobMedicalRecord"] = Relationship(
        back_populates="job_audit",
    )
    ai_ocr_records: list["SamJobMedicalRecordAiOcr"] = Relationship(
        back_populates="job_audit",
    )

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(cls, session: Session, audit_id: int) -> Union["SamJobAudit", None]:
        return session.get(cls, audit_id)

    @classmethod
    def update(
        cls, session: Session, by: str, audit_id: int, commit: bool = True, **kwargs
    ):
        instance = cls.read(session, audit_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, audit_id: int, commit: bool = True):
        return cls.update(session, by, audit_id, commit=commit, status="D")


class SamJobAuditCreate(SamJobAuditBase):
    pass


class SamJobAuditUpdate(SQLModel):
    job_state: int | None = None
    total_case: int | None = None
    complete_case: int | None = None
    process_status: str | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamJobAuditPublic(SamJobAuditBase):
    id: int


class SamJobAuditPublicWithRelated(SamJobAuditPublic):
    audit_type: "SamMasterAuditTypePublic"
    transaction: "SamJobAuditTransactionPublic"
    ai_ocr_records: list["SamJobMedicalRecordAiOcrPublic"] = []


class SamJobAuditTransactionBase(SQLModel):
    job_audit_id: int | None = Field(
        default=None,
        foreign_key="SAM_JOB_AUDIT.id",
        description="ID from SAM_JOB_AUDIT table",
        alias="JOB_AUDIT_ID",
    )
    job_state: int | None = Field(
        default=None, description="Latest processing step", alias="JOB_STATE"
    )
    seq: int | None = Field(default=None, description="Data sequence", alias="SEQ")
    hcode: str | None = Field(
        default=None, max_length=10, description="Service unit code", alias="HCODE"
    )
    hname: str | None = Field(
        default=None, max_length=255, description="Service unit name", alias="HNAME"
    )
    audit_transaction_id: str | None = Field(
        default=None,
        max_length=255,
        description="ID generated by Single Audit Platform for system-wide reference",
        alias="AUDIT_TRANSACTION_ID",
    )
    transaction_id: str | None = Field(
        default=None,
        max_length=255,
        description="Reference ID for system-wide transactions",
        alias="TRANSACTION_ID",
    )
    personal_id: str | None = Field(
        default=None, max_length=20, description="Personal ID", alias="PERSONAL_ID"
    )
    hn: str | None = Field(default=None, max_length=255, description="HN", alias="HN")
    an: str | None = Field(default=None, max_length=255, description="AN", alias="AN")
    date_admit: datetime | None = Field(
        default=None, description="Admission date", alias="DATE_ADMIT"
    )
    auditor_status: str | None = Field(
        default=None,
        max_length=20,
        description="Status of the first auditor's review",
        alias="AUDITOR_STATUS",
    )
    second_auditor_status: str | None = Field(
        default=None,
        max_length=20,
        description="Status of the second auditor's review",
        alias="SECOND_AUDITOR_STATUS",
    )
    process_status: str | None = Field(
        default=None,
        max_length=5,
        description="Data processing status",
        alias="PROCESS_STATUS",
    )
    status: str | None = Field(
        default=None, max_length=20, description="Data status", alias="STATUS"
    )
    created_date: datetime | None = Field(
        default=None,
        description="Date and time of record creation",
        alias="CREATED_DATE",
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None, description="Date and time of record update", alias="UPDATED_DATE"
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamJobAuditTransaction(SamJobAuditTransactionBase, table=True):  # type: ignore
    __tablename__ = "SAM_JOB_AUDIT_TRANSACTION"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_JOB_AUDIT_TRANSACTION").next_value(),
        primary_key=True,
        description="Generated from SAM_SEQ_JOB_AUDIT_TRANSACTION",
        alias="ID",
    )

    job_audit: "SamJobAudit" = Relationship(
        back_populates="transaction",
    )

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(
        cls, session: Session, transaction_id: int
    ) -> Union["SamJobAuditTransaction", None]:
        return session.get(cls, transaction_id)

    @classmethod
    def update(
        cls,
        session: Session,
        by: str,
        transaction_id: int,
        commit: bool = True,
        **kwargs
    ):
        instance = cls.read(session, transaction_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(
        cls, session: Session, by: str, transaction_id: int, commit: bool = True
    ):
        return cls.update(session, by, transaction_id, commit=commit, status="D")


class SamJobAuditTransactionCreate(SamJobAuditTransactionBase):
    pass


class SamJobAuditTransactionUpdate(SQLModel):
    job_state: int | None = None
    auditor_status: str | None = None
    second_auditor_status: str | None = None
    process_status: str | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamJobAuditTransactionPublic(SamJobAuditTransactionBase):
    id: int


class SamJobAuditTransactionPublicWithRelated(SamJobAuditTransactionPublic):
    job_audit: "SamJobAuditPublic"


class SamMasterAuditTypeBase(SQLModel):
    audit_category: str | None = Field(
        default=None,
        max_length=255,
        description="Type of audit (PRE = pre-payment audit, POST = post-payment audit)",
        alias="AUDIT_CATEGORY",
    )
    audit_sub_category: str | None = Field(
        default=None,
        max_length=5,
        description="Category being audited (OP, IP, PP)",
        alias="AUDIT_SUB_CATEGORY",
    )
    audit_type_number: str | None = Field(
        default=None,
        max_length=10,
        description="Code of the subject being audited",
        alias="AUDIT_TYPE_NUMBER",
    )
    audit_type_name: str | None = Field(
        default=None,
        max_length=255,
        description="Name of the subject being audited, e.g., Compensation for Cervical Cancer Screening Services (CSS) Pre-audit",
        alias="AUDIT_TYPE_NAME",
    )
    audit_form_version: str | None = Field(
        default=None,
        max_length=100,
        description="Version of the audit form",
        alias="AUDIT_FORM_VERSION",
    )
    audit_form_version_date: datetime | None = Field(
        default=None,
        description="Date of using the audit form version",
        alias="AUDIT_FORM_VERSION_DATE",
    )
    audit_round: int | None = Field(
        default=None, description="Number of rounds for the audit", alias="AUDIT_ROUND"
    )
    audit_appeal_round: int | None = Field(
        default=None,
        description="Number of rounds for the appeal (0 = no appeal, 1-3 = number of appeal rounds for that subject)",
        alias="AUDIT_APPEAL_ROUND",
    )
    status: str | None = Field(
        default=None, max_length=5, description="Data status", alias="STATUS"
    )
    created_date: datetime | None = Field(
        default=None,
        description="Date and time of record creation",
        alias="CREATED_DATE",
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None, description="Date and time of record update", alias="UPDATED_DATE"
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamMasterAuditType(SamMasterAuditTypeBase, table=True):  # type: ignore
    """
    Table for storing audit type information.
    """

    __tablename__ = "SAM_MASTER_AUDIT_TYPE"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_AUDIT_TYPE").next_value(),
        primary_key=True,
        description="Generated from SAM_SEQ_AUDIT_TYPE",
        alias="ID",
    )

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(cls, session: Session, type_id: int) -> Union["SamMasterAuditType", None]:
        return session.get(cls, type_id)

    @classmethod
    def update(
        cls, session: Session, by: str, type_id: int, commit: bool = True, **kwargs
    ):
        instance = cls.read(session, type_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, type_id: int, commit: bool = True):
        return cls.update(session, by, type_id, commit=commit, status="D")


class SamMasterAuditTypeCreate(SamMasterAuditTypeBase):
    pass


class SamMasterAuditTypeUpdate(SQLModel):
    audit_form_version: str | None = None
    audit_form_version_date: datetime | None = None
    audit_round: int | None = None
    audit_appeal_round: int | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamMasterAuditTypePublic(SamMasterAuditTypeBase):
    id: int


class SamJobMedicalRecordBase(SQLModel):
    job_audit_id: int = Field(
        foreign_key="SAM_JOB_AUDIT.id",
        description="ID from SAM_JOB_AUDIT table",
        alias="JOB_AUDIT_ID",
    )
    job_state: int | None = Field(
        default=None, description="Latest processing step", alias="JOB_STATE"
    )
    audit_transaction_id: str | None = Field(
        default=None,
        max_length=255,
        description="ID generated by Single Audit Platform for system-wide reference",
        alias="AUDIT_TRANSACTION_ID",
    )
    file_process_type: str | None = Field(
        default=None,
        max_length=5,
        description="File processing type (M = Manual pagination, A = Split by category, N = Split by file (PP examination and review))",
        alias="FILE_PROCESS_TYPE",
    )
    medical_record_status: str | None = Field(
        default=None,
        max_length=5,
        description="Medical record status (Y = Found medical record / Not accepted according to VA results, N = Medical record not found / Accepted according to VA results)",
        alias="MEDICAL_RECORD_STATUS",
    )
    process_status: str | None = Field(
        default=None,
        max_length=5,
        description="Data processing status",
        alias="PROCESS_STATUS",
    )
    status: str | None = Field(
        default=None, max_length=5, description="Data status", alias="STATUS"
    )
    created_date: datetime | None = Field(
        default=None,
        description="Date and time of record creation",
        alias="CREATED_DATE",
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None, description="Date and time of record update", alias="UPDATED_DATE"
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamJobMedicalRecord(SamJobMedicalRecordBase, table=True):  # type: ignore
    """
    Table for storing summary data of uploaded medical record documents.
    """

    __tablename__ = "SAM_JOB_MEDICAL_RECORD"  # type: ignore

    id: int = Field(
        default=Sequence(
            "SAM_SEQ_JOB_MEDICAL_RECORD"
        ).next_value(),  # Use sequence for default value
        primary_key=True,
        description="Generated from SAM_SEQ_JOB_MEDICAL_RECORD",
        alias="ID",
    )

    job_audit: "SamJobAudit" = Relationship(back_populates="medical_records")
    processes: list["SamJobMedicalRecordProcess"] = Relationship(
        back_populates="medical_record"
    )
    categories: list["SamJobMedicalRecordCategory"] = Relationship(
        back_populates="medical_record"
    )
    ai_ocr_records: list["SamJobMedicalRecordAiOcr"] = Relationship(
        back_populates="medical_record"
    )

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        """
        Creates a new SamJobMedicalRecord record.
        """
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(
        cls, session: Session, record_id: int
    ) -> Union["SamJobMedicalRecord", None]:
        """
        Retrieves a SamJobMedicalRecord record by its ID.
        """
        return session.get(cls, record_id)

    @classmethod
    def update(
        cls, session: Session, by: str, record_id: int, commit: bool = True, **kwargs
    ):
        """
        Updates a SamJobMedicalRecord record.
        """
        instance = cls.read(session, record_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, record_id: int, commit: bool = True):
        """
        Marks a SamJobMedicalRecord record as deleted.
        """
        return cls.update(session, by, record_id, commit=commit, status="D")


class SamJobMedicalRecordCreate(SamJobMedicalRecordBase):
    pass


class SamJobMedicalRecordUpdate(SQLModel):
    job_state: int | None = None
    audit_transaction_id: str | None = None
    file_process_type: str | None = None
    medical_record_status: str | None = None
    process_status: str | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamJobMedicalRecordPublic(SamJobMedicalRecordBase):
    id: int


class SamJobMedicalRecordPublicWithRelated(SamJobMedicalRecordPublic):
    processes: list["SamJobMedicalRecordProcessPublic"] = []
    categories: list["SamJobMedicalRecordCategoryPublic"] = []


class SamJobMedicalRecordProcessBase(SQLModel):
    job_medical_record_id: int | None = Field(
        default=None,
        foreign_key="SAM_JOB_MEDICAL_RECORD.id",
        description="ID from SAM_JOB_MEDICAL_RECORD",
        alias="JOB_MEDICAL_RECORD_ID",
    )
    job_audit_id: int | None = Field(
        default=None,
        foreign_key="SAM_JOB_AUDIT.id",
        description="ID from SAM_JOB_AUDIT",
        alias="JOB_AUDIT_ID",
    )
    job_state: int | None = Field(
        default=None, description="Latest processing step", alias="JOB_STATE"
    )
    audit_transaction_id: str | None = Field(
        default=None,
        max_length=255,
        description="ID generated by Single Audit Platform for system-wide reference",
        alias="AUDIT_TRANSACTION_ID",
    )
    seq: int | None = Field(default=None, description="Data sequence", alias="SEQ")
    file_name: str | None = Field(
        default=None,
        max_length=255,
        description="Uploaded file name",
        alias="FILE_NAME",
    )
    file_size: str | None = Field(
        default=None, max_length=20, description="Uploaded file size", alias="FILE_SIZE"
    )
    file_type: str | None = Field(
        default=None, max_length=20, description="Uploaded file type", alias="FILE_TYPE"
    )
    file_path: str | None = Field(
        default=None,
        max_length=255,
        description="Path of the file for display",
        alias="FILE_PATH",
    )
    file_process_category: str | None = Field(
        default=None,
        max_length=2000,
        description="File processing category in format '[1=1,3-4][2=5-7]' for each uploaded file",
        alias="FILE_PROCESS_CATEGORY",
    )
    process_status: str | None = Field(
        default=None,
        max_length=5,
        description="Data processing status",
        alias="PROCESS_STATUS",
    )
    status: str | None = Field(
        default=None, max_length=5, description="Data status", alias="STATUS"
    )
    created_date: datetime | None = Field(
        default=None, description="Creation date and time", alias="CREATED_DATE"
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None, description="Update date and time", alias="UPDATED_DATE"
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamJobMedicalRecordProcess(SamJobMedicalRecordProcessBase, table=True):  # type: ignore
    """
    Table for storing summary data of uploaded medical record documents.
    """

    __tablename__ = "SAM_JOB_MEDICAL_RECORD_PROCESS"  # type: ignore

    id: int = Field(
        default=Sequence(
            "SAM_SEQ_JOB_MEDICAL_RECORD_PROCESS"
        ).next_value(),  # Use sequence for default value
        primary_key=True,
        description="Generated from SAM_SEQ_JOB_MEDICAL_RECORD_PROCESS",
        alias="ID",
    )

    medical_record: "SamJobMedicalRecord" = Relationship(back_populates="processes")

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        """
        Creates a new SamJobMedicalRecordProcess record.
        """
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(
        cls, session: Session, process_id: int
    ) -> Union["SamJobMedicalRecordProcess", None]:
        """
        Retrieves a SamJobMedicalRecordProcess record by its ID.
        """
        return session.get(cls, process_id)

    @classmethod
    def update(
        cls, session: Session, by: str, process_id: int, commit: bool = True, **kwargs
    ):
        """
        Updates a SamJobMedicalRecordProcess record.
        """
        instance = cls.read(session, process_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, process_id: int, commit: bool = True):
        """
        Deletes a SamJobMedicalRecordProcess record.
        """
        return cls.update(session, by, process_id, commit=commit, status="D")


class SamJobMedicalRecordProcessCreate(SamJobMedicalRecordProcessBase):
    pass


class SamJobMedicalRecordProcessUpdate(SQLModel):
    job_state: int | None = None
    audit_transaction_id: str | None = None
    seq: int | None = None
    file_name: str | None = None
    file_size: str | None = None
    file_type: str | None = None
    file_path: str | None = None
    file_process_category: str | None = None
    process_status: str | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamJobMedicalRecordProcessPublic(SamJobMedicalRecordProcessBase):
    id: int


class SamJobMedicalRecordProcessPublicWithRelated(SamJobMedicalRecordProcessPublic):
    medical_record: "SamJobMedicalRecordPublic"


class SamJobMedicalRecordCategoryBase(SQLModel):
    job_medical_record_id: int = Field(
        foreign_key="SAM_JOB_MEDICAL_RECORD.id",
        description="ID from SAM_JOB_MEDICAL_RECORD table",
        alias="JOB_MEDICAL_RECORD_ID",
    )
    job_audit_id: int | None = Field(
        default=None,
        foreign_key="SAM_JOB_AUDIT.id",
        description="ID from SAM_JOB_AUDIT table",
        alias="JOB_AUDIT_ID",
    )
    job_state: int | None = Field(
        default=None, description="Latest processing step", alias="JOB_STATE"
    )
    audit_transaction_id: str | None = Field(
        default=None,
        max_length=255,
        description="ID generated by Single Audit Platform for system-wide reference",
        alias="AUDIT_TRANSACTION_ID",
    )
    seq: int | None = Field(default=None, description="Data sequence", alias="SEQ")
    file_name: str | None = Field(
        default=None,
        max_length=255,
        description="Uploaded file name",
        alias="FILE_NAME",
    )
    file_size: str | None = Field(
        default=None, max_length=20, description="Uploaded file size", alias="FILE_SIZE"
    )
    file_type: str | None = Field(
        default=None, max_length=20, description="Uploaded file type", alias="FILE_TYPE"
    )
    file_path: str | None = Field(
        default=None,
        max_length=255,
        description="Path of the file for display",
        alias="FILE_PATH",
    )
    file_category: str | None = Field(
        default=None,
        max_length=3,
        description="File category for display (15 categories and uncategorized for PP examination or review)",
        alias="FILE_CATEGORY",
    )
    file_page: str | None = Field(
        default=None,
        max_length=12,
        description="Total number of pages in the file",
        alias="FILE_PAGE",
    )
    process_status: str | None = Field(
        default=None,
        max_length=5,
        description="Data processing status",
        alias="PROCESS_STATUS",
    )
    status: str | None = Field(
        default=None, max_length=5, description="Data status", alias="STATUS"
    )
    created_date: datetime | None = Field(
        default=None, description="Creation date and time", alias="CREATED_DATE"
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None, description="Update date and time", alias="UPDATED_DATE"
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamJobMedicalRecordCategory(SamJobMedicalRecordCategoryBase, table=True):  # type: ignore
    """
    Table for storing summary data of uploaded medical record documents by category.
    """

    __tablename__ = "SAM_JOB_MEDICAL_RECORD_CATEGORY"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_JOB_MEDICAL_RECORD_CATEGORY").next_value(),
        primary_key=True,
        description="Generated from SAM_SEQ_JOB_MEDICAL_RECORD_CATEGORY",
        alias="ID",
    )

    medical_record: "SamJobMedicalRecord" = Relationship(back_populates="categories")

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(
        cls, session: Session, category_id: int
    ) -> Union["SamJobMedicalRecordCategory", None]:
        return session.get(cls, category_id)

    @classmethod
    def update(
        cls, session: Session, by: str, category_id: int, commit: bool = True, **kwargs
    ):
        instance = cls.read(session, category_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, category_id: int, commit: bool = True):
        return cls.update(session, by, category_id, commit=commit, status="D")


class SamJobMedicalRecordCategoryCreate(SamJobMedicalRecordCategoryBase):
    pass


class SamJobMedicalRecordCategoryUpdate(SQLModel):
    job_state: int | None = None
    audit_transaction_id: str | None = None
    seq: int | None = None
    file_name: str | None = None
    file_size: str | None = None
    file_type: str | None = None
    file_path: str | None = None
    file_category: str | None = None
    file_page: str | None = None
    process_status: str | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamJobMedicalRecordCategoryPublic(SamJobMedicalRecordCategoryBase):
    id: int


class SamJobMedicalRecordCategoryPublicWithRelated(SamJobMedicalRecordCategoryPublic):
    medical_record: "SamJobMedicalRecordPublic"


class SamJobMedicalRecordAiOcrBase(SQLModel):
    job_medical_record_id: int = Field(
        foreign_key="SAM_JOB_MEDICAL_RECORD.id",
        description="ID from SAM_JOB_MEDICAL_RECORD table",
        alias="JOB_MEDICAL_RECORD_ID",
    )
    job_audit_id: int | None = Field(
        default=None,
        foreign_key="SAM_JOB_AUDIT.id",
        description="ID from SAM_JOB_AUDIT table",
        alias="JOB_AUDIT_ID",
    )
    job_state: int | None = Field(
        default=None,
        description="Latest processing step",
        alias="JOB_STATE",
    )
    audit_transaction_id: str | None = Field(
        default=None,
        max_length=255,
        description="ID generated by Single Audit Platform for system-wide reference",
        alias="AUDIT_TRANSACTION_ID",
    )
    seq: int | None = Field(
        default=None,
        description="Data sequence",
        alias="SEQ",
    )
    file_name: str | None = Field(
        default=None,
        max_length=255,
        description="Uploaded file name",
        alias="FILE_NAME",
    )
    file_size: str | None = Field(
        default=None,
        max_length=20,
        description="Uploaded file size",
        alias="FILE_SIZE",
    )
    file_type: str | None = Field(
        default=None,
        max_length=20,
        description="Uploaded file type",
        alias="FILE_TYPE",
    )
    file_path: str | None = Field(
        default=None,
        max_length=255,
        description="Path of the file for display",
        alias="FILE_PATH",
    )
    file_category_tag: str | None = Field(
        default=None,
        max_length=3,
        description="File category tag found by AI from OCR",
        alias="FILE_CATEGORY_TAG",
    )
    file_page_tag: str | None = Field(
        default=None,
        max_length=3,
        description="File page tag found by AI from OCR",
        alias="FILE_PAGE_TAG",
    )
    tag_process: str | None = Field(
        default=None,
        max_length=2000,
        description="Tags found compared to INCOM based on NHSO conditions",
        alias="TAG_PROCESS",
    )
    process_status: str | None = Field(
        default=None,
        max_length=5,
        description="Data processing status",
        alias="PROCESS_STATUS",
    )
    status: str | None = Field(
        default=None,
        max_length=5,
        description="Data status",
        alias="STATUS",
    )
    created_date: datetime | None = Field(
        default=None,
        description="Creation date and time",
        alias="CREATED_DATE",
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None,
        description="Update date and time",
        alias="UPDATED_DATE",
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamJobMedicalRecordAiOcr(SamJobMedicalRecordAiOcrBase, table=True):  # type: ignore
    __tablename__ = "SAM_JOB_MEDICAL_RECORD_AI_OCR"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_JOB_MEDICAL_RECORD_AI_OCR").next_value(),
        primary_key=True,
        description="Generated from SAM_SEQ_JOB_MEDICAL_RECORD_AI_OCR",
        alias="ID",
    )

    medical_record: "SamJobMedicalRecord" = Relationship(
        back_populates="ai_ocr_records"
    )
    job_audit: "SamJobAudit" = Relationship(back_populates="ai_ocr_records")

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(
        cls, session: Session, ocr_id: int
    ) -> Union["SamJobMedicalRecordAiOcr", None]:
        return session.get(cls, ocr_id)

    @classmethod
    def update(
        cls, session: Session, by: str, ocr_id: int, commit: bool = True, **kwargs
    ):
        instance = cls.read(session, ocr_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, ocr_id: int, commit: bool = True):
        return cls.update(session, by, ocr_id, commit=commit, status="D")


class SamJobMedicalRecordAiOcrCreate(SamJobMedicalRecordAiOcrBase):
    pass


class SamJobMedicalRecordAiOcrUpdate(SQLModel):
    job_state: int | None = None
    audit_transaction_id: str | None = None
    seq: int | None = None
    file_name: str | None = None
    file_size: str | None = None
    file_type: str | None = None
    file_path: str | None = None
    file_category_tag: str | None = None
    file_page_tag: str | None = None
    tag_process: str | None = None
    process_status: str | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamJobMedicalRecordAiOcrPublic(SamJobMedicalRecordAiOcrBase):
    id: int


class SamJobMedicalRecordAiOcrPublicWithRelated(SamJobMedicalRecordAiOcrPublic):
    medical_record: "SamJobMedicalRecordPublic"
    job_audit: "SamJobAuditPublic"


class SamMasterAuditorBase(SQLModel):
    auditor_code: str | None = Field(
        default=None,
        max_length=20,
        description="Auditor code categorized by type (D = Doctor, N = Nurse, C = Coder)",
        alias="AUDITOR_CODE",
    )
    personal_id: str | None = Field(
        default=None,
        max_length=13,
        description="13-digit personal ID",
        alias="PERSONAL_ID",
    )
    position: str | None = Field(
        default=None,
        max_length=2,
        description="Position (DO = Doctor, NU = Nurse, etc.)",
        alias="POSITION",
    )
    register_number: int | None = Field(
        default=None,
        description="Registration number",
        alias="REGISTER_NUMBER",
    )
    first_name: str | None = Field(
        default=None,
        max_length=100,
        description="First name",
        alias="FIRST_NAME",
    )
    last_name: str | None = Field(
        default=None,
        max_length=100,
        description="Last name",
        alias="LAST_NAME",
    )
    gender: int | None = Field(
        default=None,
        description="Gender (1 = Male, 2 = Female)",
        alias="GENDER",
    )
    phone: str | None = Field(
        default=None,
        max_length=100,
        description="Phone number",
        alias="PHONE",
    )
    mobile: str | None = Field(
        default=None,
        max_length=100,
        description="Mobile number",
        alias="MOBILE",
    )
    province_id: str | None = Field(
        default=None,
        max_length=4,
        description="ID of the province of work",
        alias="PROVINCE_ID",
    )
    nhso_zone: str | None = Field(
        default=None,
        max_length=2,
        description="ID of the working zone",
        alias="NHSO_ZONE",
    )
    hcode: str | None = Field(
        default=None,
        max_length=10,
        description="Service unit code",
        alias="HCODE",
    )
    doctor_license: str | None = Field(
        default=None,
        max_length=10,
        description="Doctor's license number",
        alias="DOCTOR_LICENSE",
    )
    major_license: str | None = Field(
        default=None,
        max_length=5,
        description="Specialty certification",
        alias="MAJOR_LICENSE",
    )
    vendor_code: str | None = Field(
        default=None,
        max_length=10,
        description="Vendor code from SAP system",
        alias="VENDOR_CODE",
    )
    standard_test: int | None = Field(
        default=None,
        description="Standard assessment result",
        alias="STANDARD_TEST",
    )
    register_year: str | None = Field(
        default=None,
        max_length=4,
        description="Year of registration",
        alias="REGISTER_YEAR",
    )
    tha_id_consent: int | None = Field(
        default=None,
        description="Identity confirmation through THAID system (1 = confirmed)",
        alias="THAID_CONSENT",
    )
    tha_id_consent_fname: str | None = Field(
        default=None,
        max_length=100,
        description="First name confirmed through THAID",
        alias="THAID_CONSENT_FNAME",
    )
    tha_id_consent_lname: str | None = Field(
        default=None,
        max_length=100,
        description="Last name confirmed through THAID",
        alias="THAID_CONSENT_LNAME",
    )
    tha_id_consent_pid: str | None = Field(
        default=None,
        max_length=13,
        description="Personal ID confirmed through THAID",
        alias="THAID_CONSENT_PID",
    )
    tha_id_consent_date: datetime | None = Field(
        default=None,
        description="Date of identity confirmation",
        alias="THAID_CONSENT_DATE",
    )
    tha_id_consent_token: str | None = Field(
        default=None,
        max_length=255,
        description="Token from THAID system for identity confirmation",
        alias="THAID_CONSENT_TOKEN",
    )
    status: str | None = Field(
        default=None,
        max_length=5,
        description="Data status",
        alias="STATUS",
    )
    created_date: datetime | None = Field(
        default=None,
        description="Creation date and time",
        alias="CREATED_DATE",
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None,
        description="Update date and time",
        alias="UPDATED_DATE",
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamMasterAuditor(SamMasterAuditorBase, table=True):  # type: ignore
    __tablename__ = "SAM_MASTER_AUDITOR"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_MASTER_AUDITOR").next_value(),
        primary_key=True,
        description="Generated from SAM_SEQ_MASTER_AUDITOR",
        alias="ID",
    )

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(cls, session: Session, auditor_id: int) -> Union["SamMasterAuditor", None]:
        return session.get(cls, auditor_id)

    @classmethod
    def update(
        cls, session: Session, by: str, auditor_id: int, commit: bool = True, **kwargs
    ):
        instance = cls.read(session, auditor_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, auditor_id: int, commit: bool = True):
        return cls.update(session, by, auditor_id, commit=commit, status="D")


class SamMasterAuditorDocumentBase(SQLModel):
    master_auditor_id: int = Field(
        foreign_key="SAM_MASTER_AUDITOR.id",
        description="ID from SAM_MASTER_AUDITOR table",
        alias="MASTER_AUDITOR_ID",
    )
    seq: int | None = Field(default=None, description="Data sequence", alias="SEQ")
    file_name: str | None = Field(
        default=None, max_length=255, description="Uploaded file name", alias="FILE_NAME"
    )
    file_path: str | None = Field(
        default=None, max_length=255, description="Path of the file for display", alias="FILE_PATH"
    )
    file_size: str | None = Field(
        default=None, max_length=20, description="Uploaded file size", alias="FILE_SIZE"
    )
    file_type: str | None = Field(
        default=None, max_length=20, description="Uploaded file type", alias="FILE_TYPE"
    )
    status: str | None = Field(default=None, max_length=5, description="Data status", alias="STATUS")
    created_date: datetime | None = Field(
        default=None, description="Creation date and time", alias="CREATED_DATE"
    )
    created_by: str | None = Field(
        default=None, max_length=255, description="User who created the record", alias="CREATED_BY"
    )
    updated_date: datetime | None = Field(
        default=None, description="Update date and time", alias="UPDATED_DATE"
    )
    updated_by: str | None = Field(
        default=None, max_length=255, description="User who updated the record", alias="UPDATED_BY"
    )


class SamMasterAuditorDocument(SamMasterAuditorDocumentBase, table=True):  # type: ignore
    __tablename__ = "SAM_MASTER_AUDITOR_DOCUMENT"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_MASTER_AUDITOR_DOCUMENT").next_value(),
        primary_key=True,
        alias="ID",
    )

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(cls, session: Session, document_id: int):
        return session.get(cls, document_id)


class SamMasterAuditorDocumentCreate(SamMasterAuditorDocumentBase):
    pass


class SamMasterAuditorDocumentUpdate(SQLModel):
    seq: int | None = None
    file_name: str | None = None
    file_path: str | None = None
    file_size: str | None = None
    file_type: str | None = None
    file_detail: str | None = None
    status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamMasterAuditorDocumentPublic(SamMasterAuditorDocumentBase):
    id: int


class SamPaKidneyUploadBase(SQLModel):
    pa_kidney_id: int | None = Field(
        default=None,
        description="ID from PA Kidney table",
        alias="PA_KIDNEY_ID",
    )
    pa_kidney_lab_id: int | None = Field(
        default=None,
        description="ID from PA Kidney Lab table",
        alias="PA_KIDNEY_LAB_ID",
    )
    pa_kidney_ip_detail_id: int | None = Field(
        default=None,
        description="ID from PA Kidney IP Detail table",
        alias="PA_KIDNEY_IP_DETAIL_ID",
    )
    pa_kidney_appeal_id: int | None = Field(
        default=None,
        description="ID from PA Kidney Appeal table",
        alias="PA_KIDNEY_APPEAL_ID",
    )
    seq: int | None = Field(
        default=None,
        description="Sequence number for uploaded files",
        alias="SEQ",
    )
    type: str | None = Field(
        default=None,
        max_length=255,
        description="Type of upload",
        alias="TYPE",
    )
    file_name: str | None = Field(
        default=None,
        max_length=500,
        description="Uploaded file name",
        alias="FILE_NAME",
    )
    file_type: str | None = Field(
        default=None,
        max_length=255,
        description="Uploaded file type",
        alias="FILE_TYPE",
    )
    file_size: int | None = Field(
        default=None,
        description="Uploaded file size in bytes",
        alias="FILE_SIZE",
    )
    file_path: str | None = Field(
        default=None,
        max_length=500,
        description="Path of the file for display",
        alias="FILE_PATH",
    )
    file_detail: str | None = Field(
        default=None,
        max_length=500,
        description="Detail of the file",
        alias="FILE_DETAIL",
    )
    status: str | None = Field(
        default=None,
        max_length=5,
        description="Data status",
        alias="STATUS",
    )
    process_status: str | None = Field(
        default=None,
        max_length=5,
        description="Data processing status",
        alias="PROCESS_STATUS",
    )
    created_date: datetime | None = Field(
        default=None,
        description="Date and time of record creation",
        alias="CREATED_DATE",
    )
    created_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who created the record",
        alias="CREATED_BY",
    )
    updated_date: datetime | None = Field(
        default=None,
        description="Date and time of record update",
        alias="UPDATED_DATE",
    )
    updated_by: str | None = Field(
        default=None,
        max_length=255,
        description="User who updated the record",
        alias="UPDATED_BY",
    )


class SamPaKidneyUpload(SamPaKidneyUploadBase, table=True):  # type: ignore
    """
    Table for storing kidney upload files.
    """
    __tablename__ = "SAM_PA_KIDNEY_UPLOAD"  # type: ignore

    id: int = Field(
        default=Sequence("SAM_SEQ_PA_KIDNEY_UPLOAD").next_value(),
        primary_key=True,
        description="Generated from SAM_SEQ_PA_KIDNEY_UPLOAD",
        alias="ID",
    )

    @classmethod
    def create(cls, session: Session, commit: bool = True, **kwargs):
        instance = cls(**kwargs)
        session.add(instance)
        if commit:
            session.commit()
        else:
            session.flush()
        session.refresh(instance)
        return instance

    @classmethod
    def read(cls, session: Session, upload_id: int) -> Union["SamPaKidneyUpload", None]:
        return session.get(cls, upload_id)

    @classmethod
    def update(
        cls, session: Session, by: str, upload_id: int, commit: bool = True, **kwargs
    ):
        instance = cls.read(session, upload_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            instance.updated_date = get_current_bangkok_datetime()
            instance.updated_by = by
            if commit:
                session.commit()
            else:
                session.flush()
        return instance

    @classmethod
    def delete(cls, session: Session, by: str, upload_id: int, commit: bool = True):
        return cls.update(session, by, upload_id, commit=commit, status="D")


class SamPaKidneyUploadCreate(SamPaKidneyUploadBase):
    pass


class SamPaKidneyUploadUpdate(SQLModel):
    pa_kidney_id: int | None = None
    pa_kidney_lab_id: int | None = None
    pa_kidney_ip_detail_id: int | None = None
    pa_kidney_appeal_id: int | None = None
    seq: int | None = None
    type: str | None = None
    file_name: str | None = None
    file_type: str | None = None
    file_size: int | None = None
    file_path: str | None = None
    file_detail: str | None = None
    status: str | None = None
    process_status: str | None = None
    updated_date: datetime | None = None
    updated_by: str | None = None


class SamPaKidneyUploadPublic(SamPaKidneyUploadBase):
    id: int
