FROM python:3.12-slim

WORKDIR /app

COPY requirements-api-dev.txt .
RUN pip install --no-cache-dir -r requirements-api-dev.txt

# Install required packages for Oracle Instant Client
RUN apt-get update && \
    apt-get install -y unzip libaio1t64 && \
    ln -s /usr/lib/x86_64-linux-gnu/libaio.so.1t64 /usr/lib/x86_64-linux-gnu/libaio.so.1 && \
    apt-get clean

# Copy and unzip the Oracle Instant Client
COPY instantclient-basic-linux.x64-*********.0dbru.zip ./
RUN unzip instantclient-basic-linux.x64-*********.0dbru.zip -d /opt/oracle && \
    rm instantclient-basic-linux.x64-*********.0dbru.zip

# Set environment variables for Oracle Instant Client
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_19_24:$LD_LIBRARY_PATH
ENV PATH=/opt/oracle/instantclient_19_24:$PATH

COPY app ./app

CMD ["gunicorn", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:3000", "app.main:app", "--workers", "4"]